using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Core.Aspects.Autofac.Caching;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Entities.Concrete;
using System.Collections.Generic;

namespace Business.Concrete
{
    public class LicenseTransactionManager : ILicenseTransactionService
    {
        private readonly ILicenseTransactionDal _licenseTransactionDal;

        public LicenseTransactionManager(ILicenseTransactionDal licenseTransactionDal)
        {
            _licenseTransactionDal = licenseTransactionDal;
        }
        [SecuredOperation("owner")]
        [CacheInvalidationAspect("LicenseTransaction", "UserLicense")]
        [LogAspect]
        [PerformanceAspect(3)]
        public IResult Add(LicenseTransaction licenseTransaction)
        {
            return _licenseTransactionDal.AddLicenseTransaction(licenseTransaction);
        }

        [SecuredOperation("owner")]
        [CacheAspect(duration: 1800)] // 30 dakika - License transaction listesi
        [PerformanceAspect(3)]
        public IDataResult<List<LicenseTransaction>> GetAll()
        {
            return _licenseTransactionDal.GetAllOrderedByDate();
        }

        [SecuredOperation("owner")]
        [CacheAspect(duration: 600)] // 10 dakika - Filtrelenmiş license transaction sorguları
        [PerformanceAspect(3)]
        public IDataResult<List<LicenseTransaction>> GetAllFiltered(int? userID, string startDate, string endDate, int page, int pageSize)
        {
            return _licenseTransactionDal.GetAllFiltered(userID, startDate, endDate, page, pageSize);
        }

        [SecuredOperation("owner")]
        [CacheAspect(duration: 1800)] // 30 dakika - Kullanıcı license transaction'ları
        [PerformanceAspect(3)]
        public IDataResult<List<LicenseTransaction>> GetByUserId(int userId)
        {
            return new SuccessDataResult<List<LicenseTransaction>>(
                _licenseTransactionDal.GetAll(lt => lt.UserID == userId && lt.IsActive == true));
        }

        [SecuredOperation("owner")]
        [CacheInvalidationAspect("LicenseTransaction", "UserLicense")]
        [LogAspect]
        [PerformanceAspect(3)]
        public IResult Delete(int id)
        {
            return _licenseTransactionDal.SoftDeleteLicenseTransaction(id);
        }

        [SecuredOperation("owner")]
        [CacheAspect(duration: 3600)] // 1 saat - License transaction toplamları
        [PerformanceAspect(3)]
        public IDataResult<object> GetTotals()
        {
            return _licenseTransactionDal.GetTotalAmountsByPaymentMethod();
        }

        [SecuredOperation("owner")]
        [CacheAspect(duration: 3600)] // 1 saat - Aylık license gelir trendi
        [PerformanceAspect(3)]
        public IDataResult<object> GetMonthlyRevenue(int year)
        {
            return _licenseTransactionDal.GetMonthlyRevenueByYear(year);
        }
    }
}