using Business.Abstract;
using Core.Utilities.Results;
using Business.BusinessAscpects;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using System.Security.Cryptography;
using System.Text;
using Core.CrossCuttingConcerns.Caching;

namespace Business.Concrete
{
    public class AdvancedRateLimitManager : IAdvancedRateLimitService
    {
        private readonly ICacheManager _cacheManager;

        // Login Rate Limiting Constants
        private const int MAX_LOGIN_ATTEMPTS = 5;
        private readonly int[] LOGIN_BAN_DURATIONS = { 5, 15, 60, 360, 1440 }; // dakika: 5dk, 15dk, 1saat, 6saat, 24saat

        // Register Rate Limiting Constants
        private const int MAX_REGISTER_ATTEMPTS = 3;
        private readonly int[] REGISTER_BAN_DURATIONS = { 60, 360, 1440, 10080 }; // dakika: 1saat, 6saat, 24saat, 7gün

        // Profile Image Upload Rate Limiting Constants
        private const int MAX_PROFILE_IMAGE_UPLOADS_PER_DAY = 3;

        // File Download Rate Limiting Constants
        private const int MAX_FILE_DOWNLOADS_PER_10_MINUTES = 5;

        public AdvancedRateLimitManager(ICacheManager cacheManager)
        {
            _cacheManager = cacheManager;
        }

        [LogAspect]
        [PerformanceAspect(3)]
        public IResult CheckLoginAttempt(string ipAddress, string deviceFingerprint)
        {
            // Sadece device fingerprint tabanlı ban kontrolü
            // IP tabanlı ban kullanmıyoruz çünkü paylaşılan IP'lerde (spor salonu WiFi vb.)
            // diğer kullanıcıları da etkileyebilir
            var banKey = GetLoginBanKey(ipAddress, deviceFingerprint);

            var banEndTime = _cacheManager.Get<DateTime?>("RateLimit", banKey);
            if (banEndTime.HasValue)
            {
                var remaining = banEndTime.Value - DateTime.Now;

                if (remaining.TotalMinutes > 0)
                {
                    var remainingMinutes = Math.Ceiling(remaining.TotalMinutes);
                    return new ErrorResult($"Çok fazla başarısız giriş denemesi. Lütfen {remainingMinutes} dakika sonra tekrar deneyin.");
                }
                else
                {
                    // Ban süresi dolmuş, cache'den temizle
                    _cacheManager.Remove("RateLimit", banKey);
                }
            }

            return new SuccessResult();
        }

        [LogAspect]
        [PerformanceAspect(3)]
        public IResult RecordFailedLogin(string ipAddress, string deviceFingerprint)
        {
            var failKey = GetLoginFailKey(ipAddress, deviceFingerprint);
            var banKey = GetLoginBanKey(ipAddress, deviceFingerprint);
            var violationKey = GetLoginViolationKey(ipAddress, deviceFingerprint);

            int failCount = _cacheManager.Get<int>("RateLimit", failKey);
            failCount++;

            if (failCount >= MAX_LOGIN_ATTEMPTS)
            {
                // Violation sayısını al
                int violationCount = _cacheManager.Get<int>("RateLimit", violationKey);

                // Progressive ban süresi hesapla
                int banDuration = GetProgressiveBanDuration(violationCount, LOGIN_BAN_DURATIONS);
                var banEndTime = DateTime.Now.AddMinutes(banDuration);

                // Sadece device fingerprint tabanlı ban uygula
                _cacheManager.Set("RateLimit", banKey, banEndTime, TimeSpan.FromMinutes(banDuration));

                // Violation sayısını artır
                violationCount++;
                _cacheManager.Set("RateLimit", violationKey, violationCount, TimeSpan.FromMinutes(10080)); // 7 gün boyunca violation sayısını tut

                // Fail count'u sıfırla
                _cacheManager.Remove("RateLimit", failKey);



                return new ErrorResult($"Çok fazla başarısız giriş denemesi. {banDuration} dakika boyunca giriş yapamazsınız.");
            }
            else
            {
                // Fail count'u güncelle (5 dakika boyunca tut)
                _cacheManager.Set("RateLimit", failKey, failCount, TimeSpan.FromMinutes(5));
            }

            return new SuccessResult();
        }

        [LogAspect]
        [PerformanceAspect(3)]
        public IResult RecordSuccessfulLogin(string ipAddress, string deviceFingerprint)
        {
            var failKey = GetLoginFailKey(ipAddress, deviceFingerprint);
            var banKey = GetLoginBanKey(ipAddress, deviceFingerprint);

            // Başarılı giriş sonrası sadece device fingerprint tabanlı fail count'u ve ban'ı sıfırla
            _cacheManager.Remove("RateLimit", failKey);
            _cacheManager.Remove("RateLimit", banKey);

            return new SuccessResult();
        }

        [LogAspect]
        [PerformanceAspect(3)]
        public IDataResult<int> GetRemainingLoginBanTime(string ipAddress, string deviceFingerprint)
        {
            var banKey = GetLoginBanKey(ipAddress, deviceFingerprint);



            var banEndTime = _cacheManager.Get<DateTime?>("RateLimit", banKey);
            if (banEndTime.HasValue)
            {
                var remaining = banEndTime.Value - DateTime.Now;

                if (remaining.TotalMinutes > 0)
                {
                    return new SuccessDataResult<int>((int)Math.Ceiling(remaining.TotalMinutes));
                }
                else
                {
                    // Ban süresi dolmuş, cache'den temizle
                    _cacheManager.Remove("RateLimit", banKey);
                }
            }

            return new SuccessDataResult<int>(0);
        }

        [LogAspect]
        [PerformanceAspect(3)]
        public IResult CheckRegisterAttempt(string ipAddress)
        {
            var banKey = GetRegisterBanKey(ipAddress);

            if (_memoryCache.TryGetValue(banKey, out DateTime banEndTime))
            {
                var remaining = banEndTime - DateTime.Now;

                if (remaining.TotalMinutes > 0)
                {
                    var remainingText = remaining.TotalDays >= 1
                        ? $"{Math.Ceiling(remaining.TotalDays)} gün"
                        : $"{Math.Ceiling(remaining.TotalMinutes)} dakika";

                    return new ErrorResult($"Çok fazla kayıt denemesi. Lütfen {remainingText} sonra tekrar deneyin.");
                }
                else
                {
                    _memoryCache.Remove(banKey);
                }
            }

            return new SuccessResult();
        }

        [LogAspect]
        [PerformanceAspect(3)]
        public IResult RecordSuccessfulRegister(string ipAddress)
        {
            var countKey = GetRegisterCountKey(ipAddress);
            var banKey = GetRegisterBanKey(ipAddress);
            var violationKey = GetRegisterViolationKey(ipAddress);

            int registerCount = 0;
            if (_memoryCache.TryGetValue(countKey, out int existingRegisterCount))
            {
                registerCount = existingRegisterCount;
            }
            registerCount++;

            if (registerCount >= MAX_REGISTER_ATTEMPTS)
            {
                // Violation sayısını al
                int violationCount = 0;
                if (_memoryCache.TryGetValue(violationKey, out int existingViolationCount))
                {
                    violationCount = existingViolationCount;
                }

                // Progressive ban süresi hesapla
                int banDuration = GetProgressiveBanDuration(violationCount, REGISTER_BAN_DURATIONS);
                var banEndTime = DateTime.Now.AddMinutes(banDuration);

                // Ban uygula
                _memoryCache.Set(banKey, banEndTime, TimeSpan.FromMinutes(banDuration));

                // Violation sayısını artır
                violationCount++;
                _memoryCache.Set(violationKey, violationCount, TimeSpan.FromMinutes(20160)); // 14 gün boyunca violation sayısını tut

                // Register count'u sıfırla
                _memoryCache.Remove(countKey);

                var banText = banDuration >= 1440
                    ? $"{banDuration / 1440} gün"
                    : $"{banDuration / 60} saat";

                return new ErrorResult($"Günlük kayıt limitini aştınız. {banText} boyunca kayıt yapamazsınız.");
            }
            else
            {
                // Register count'u güncelle (24 saat boyunca tut)
                _memoryCache.Set(countKey, registerCount, TimeSpan.FromMinutes(1440));
            }

            return new SuccessResult();
        }

        [LogAspect]
        [PerformanceAspect(3)]
        public IDataResult<int> GetRemainingRegisterBanTime(string ipAddress)
        {
            var banKey = GetRegisterBanKey(ipAddress);

            if (_memoryCache.TryGetValue(banKey, out DateTime banEndTime))
            {
                var remaining = banEndTime - DateTime.Now;

                if (remaining.TotalMinutes > 0)
                {
                    return new SuccessDataResult<int>((int)Math.Ceiling(remaining.TotalMinutes));
                }
            }

            return new SuccessDataResult<int>(0);
        }

        [LogAspect]
        [PerformanceAspect(3)]
        public IResult CheckProfileImageUploadAttempt(int userId)
        {
            var uploadCountKey = GetProfileImageUploadCountKey(userId);

            int uploadCount = 0;
            if (_memoryCache.TryGetValue(uploadCountKey, out int existingUploadCount))
            {
                uploadCount = existingUploadCount;
            }

            if (uploadCount >= MAX_PROFILE_IMAGE_UPLOADS_PER_DAY)
            {
                return new ErrorResult($"Günlük profil fotoğrafı yükleme limitini aştınız. Günde en fazla {MAX_PROFILE_IMAGE_UPLOADS_PER_DAY} fotoğraf yükleyebilirsiniz.");
            }

            return new SuccessResult();
        }

        [LogAspect]
        [PerformanceAspect(3)]
        public IResult RecordProfileImageUpload(int userId)
        {
            var uploadCountKey = GetProfileImageUploadCountKey(userId);

            int uploadCount = 0;
            if (_memoryCache.TryGetValue(uploadCountKey, out int existingUploadCount2))
            {
                uploadCount = existingUploadCount2;
            }
            uploadCount++;

            if (uploadCount > MAX_PROFILE_IMAGE_UPLOADS_PER_DAY)
            {
                return new ErrorResult($"Günlük profil fotoğrafı yükleme limitini aştınız. Günde en fazla {MAX_PROFILE_IMAGE_UPLOADS_PER_DAY} fotoğraf yükleyebilirsiniz.");
            }

            // Günün sonuna kadar cache'te tut (gece yarısına kadar)
            var endOfDay = DateTime.Today.AddDays(1);
            var minutesUntilEndOfDay = (int)(endOfDay - DateTime.Now).TotalMinutes;

            _memoryCache.Set(uploadCountKey, uploadCount, TimeSpan.FromMinutes(minutesUntilEndOfDay));

            return new SuccessResult();
        }

        [LogAspect]
        [PerformanceAspect(3)]
        public IDataResult<int> GetRemainingProfileImageUploads(int userId)
        {
            var uploadCountKey = GetProfileImageUploadCountKey(userId);

            int uploadCount = 0;
            if (_memoryCache.TryGetValue(uploadCountKey, out int existingUploadCount3))
            {
                uploadCount = existingUploadCount3;
            }

            int remaining = MAX_PROFILE_IMAGE_UPLOADS_PER_DAY - uploadCount;
            return new SuccessDataResult<int>(Math.Max(0, remaining));
        }

        [LogAspect]
        [PerformanceAspect(3)]
        public IResult CheckFileDownloadAttempt(int userId)
        {
            var downloadCountKey = GetFileDownloadCountKey(userId);

            int downloadCount = 0;
            if (_memoryCache.TryGetValue(downloadCountKey, out int existingDownloadCount))
            {
                downloadCount = existingDownloadCount;
            }

            if (downloadCount >= MAX_FILE_DOWNLOADS_PER_10_MINUTES)
            {
                return new ErrorResult($"Dosya indirme limitini aştınız. 10 dakikada en fazla {MAX_FILE_DOWNLOADS_PER_10_MINUTES} dosya indirebilirsiniz.");
            }

            return new SuccessResult();
        }

        [LogAspect]
        [PerformanceAspect(3)]
        public IResult RecordFileDownload(int userId)
        {
            var downloadCountKey = GetFileDownloadCountKey(userId);

            int downloadCount = 0;
            if (_memoryCache.TryGetValue(downloadCountKey, out int existingDownloadCount2))
            {
                downloadCount = existingDownloadCount2;
            }
            downloadCount++;

            if (downloadCount > MAX_FILE_DOWNLOADS_PER_10_MINUTES)
            {
                return new ErrorResult($"Dosya indirme limitini aştınız. 10 dakikada en fazla {MAX_FILE_DOWNLOADS_PER_10_MINUTES} dosya indirebilirsiniz.");
            }

            // 10 dakika boyunca cache'te tut
            _memoryCache.Set(downloadCountKey, downloadCount, TimeSpan.FromMinutes(10));

            return new SuccessResult();
        }

        [LogAspect]
        [PerformanceAspect(3)]
        public IDataResult<int> GetRemainingFileDownloads(int userId)
        {
            var downloadCountKey = GetFileDownloadCountKey(userId);

            int downloadCount = 0;
            if (_memoryCache.TryGetValue(downloadCountKey, out int existingDownloadCount3))
            {
                downloadCount = existingDownloadCount3;
            }

            int remaining = MAX_FILE_DOWNLOADS_PER_10_MINUTES - downloadCount;
            return new SuccessDataResult<int>(Math.Max(0, remaining));
        }

        public string GenerateDeviceFingerprint(string ipAddress, string userAgent, string deviceInfo)
        {
            var combinedInfo = $"{ipAddress}|{userAgent}|{deviceInfo}";
            using (var sha256 = SHA256.Create())
            {
                var hashBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(combinedInfo));
                return Convert.ToBase64String(hashBytes);
            }
        }

        // Private Helper Methods
        private int GetProgressiveBanDuration(int violationCount, int[] banDurations)
        {
            if (violationCount >= banDurations.Length)
            {
                return banDurations[banDurations.Length - 1]; // Son değeri kullan
            }
            return banDurations[violationCount];
        }

        private string GetLoginFailKey(string ipAddress, string deviceFingerprint)
        {
            return $"login_fail:{ipAddress}:{deviceFingerprint}";
        }

        private string GetLoginBanKey(string ipAddress, string deviceFingerprint)
        {
            return $"login_ban:{ipAddress}:{deviceFingerprint}";
        }

        private string GetLoginViolationKey(string ipAddress, string deviceFingerprint)
        {
            return $"login_violation:{ipAddress}:{deviceFingerprint}";
        }

        private string GetRegisterCountKey(string ipAddress)
        {
            var today = DateTime.Now.ToString("yyyy-MM-dd");
            return $"register_count:{ipAddress}:{today}";
        }

        private string GetRegisterBanKey(string ipAddress)
        {
            return $"register_ban:{ipAddress}";
        }

        private string GetRegisterViolationKey(string ipAddress)
        {
            return $"register_violation:{ipAddress}";
        }

        private string GetProfileImageUploadCountKey(int userId)
        {
            var today = DateTime.Now.ToString("yyyy-MM-dd");
            return $"profile_image_upload:{userId}:{today}";
        }

        private string GetFileDownloadCountKey(int userId)
        {
            var tenMinuteSlot = DateTime.Now.ToString("yyyy-MM-dd-HH") + "-" + (DateTime.Now.Minute / 10);
            return $"file_download:{userId}:{tenMinuteSlot}";
        }
    }
}
